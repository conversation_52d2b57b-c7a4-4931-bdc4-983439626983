import { Injectable, HttpException, HttpStatus, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { ChatOpenAI } from '@langchain/openai';
import { HumanMessage, AIMessage, BaseMessage } from '@langchain/core/messages';
import { DomainQuestionsService } from '../domain-questions/domain-questions.service';
import { TakeHomeTaskService } from '../take-home/take-home-task.service';
import { LiveCodingService } from '../live-coding/live-coding.service';
import { liveCodingDto } from '../live-coding/live-coding.dto';
import { CreateDomainAssessmentDto } from '../domain-questions/dto/domain-filter.dto';
import { ChatSession, ChatResponse, AssessmentType, ConversationDto, StructuredQuestion, DomainQuestion } from './assessmentChatbot.types';

// Static question bank organized by programming topics
const QUESTION_BANK = {
  "binary search tree": [
    {
      title: "BST Insertion",
      description: "Implement a function to insert a node into a Binary Search Tree while maintaining the BST property.",
    },
    {
      title: "BST Validation",
      description: "Write a function to determine if a given binary tree is a valid Binary Search Tree.",
    },
    {
      title: "BST Traversal",
      description: "Implement in-order, pre-order, and post-order traversals of a Binary Search Tree.",
    },
    {
      title: "Find Closest Value in BST",
      description: "Write a function that finds the closest value to a given target value in a BST.",
    },
    {
      title: "BST Deletion",
      description: "Implement a function to delete a node from a Binary Search Tree while maintaining the BST property.",
    },
  ],
  "dynamic programming": [
    {
      title: "Fibonacci Sequence",
      description: "Implement the Fibonacci sequence using dynamic programming to optimize performance.",
    },
    {
      title: "Longest Common Subsequence",
      description: "Find the longest common subsequence between two strings using dynamic programming.",
    },
    {
      title: "Knapsack Problem",
      description: "Solve the 0/1 knapsack problem using dynamic programming.",
    },
    {
      title: "Coin Change Problem",
      description: "Find the minimum number of coins needed to make a specific amount using dynamic programming.",
    },
    {
      title: "Longest Increasing Subsequence",
      description: "Find the length of the longest increasing subsequence in an array using dynamic programming.",
    },
  ],
  "array": [
    {
      title: "Two Sum",
      description: "Find two numbers in an array that add up to a specific target.",
    },
    {
      title: "Maximum Subarray",
      description: "Find the contiguous subarray with the largest sum.",
    },
    {
      title: "Merge Sorted Arrays",
      description: "Merge two sorted arrays into a single sorted array.",
    },
    {
      title: "Product of Array Except Self",
      description: "Calculate the product of all elements except the current one without using division.",
    },
    {
      title: "Container With Most Water",
      description: "Find two lines that together with the x-axis form a container that holds the most water.",
    },
  ],
  "string": [
    {
      title: "Valid Anagram",
      description: "Determine if two strings are anagrams of each other.",
    },
    {
      title: "Longest Substring Without Repeating Characters",
      description: "Find the length of the longest substring without repeating characters.",
    },
    {
      title: "String to Integer (atoi)",
      description: "Implement a function that converts a string to an integer.",
    },
    {
      title: "Valid Palindrome",
      description: "Determine if a string is a valid palindrome considering only alphanumeric characters.",
    },
    {
      title: "Group Anagrams",
      description: "Group a list of strings such that all anagrams are in the same group.",
    },
  ],
  "linked list": [
    {
      title: "Reverse Linked List",
      description: "Reverse a singly linked list.",
    },
    {
      title: "Detect Cycle",
      description: "Determine if a linked list has a cycle.",
    },
    {
      title: "Merge Two Sorted Lists",
      description: "Merge two sorted linked lists into a single sorted linked list.",
    },
    {
      title: "Remove Nth Node From End",
      description: "Remove the nth node from the end of a linked list.",
    },
    {
      title: "Linked List Intersection",
      description: "Find the node where two linked lists intersect.",
    },
  ],
  "graph": [
    {
      title: "Breadth-First Search",
      description: "Implement BFS to traverse a graph and find the shortest path between two nodes.",
    },
    {
      title: "Depth-First Search",
      description: "Implement DFS to traverse a graph and detect cycles.",
    },
    {
      title: "Dijkstra's Algorithm",
      description: "Implement Dijkstra's algorithm to find the shortest path in a weighted graph.",
    },
    {
      title: "Topological Sort",
      description: "Implement a topological sorting algorithm for a directed acyclic graph.",
    },
    {
      title: "Minimum Spanning Tree",
      description: "Implement Kruskal's or Prim's algorithm to find the minimum spanning tree of a graph.",
    },
  ],
  "sorting": [
    {
      title: "Merge Sort",
      description: "Implement the merge sort algorithm to sort an array.",
    },
    {
      title: "Quick Sort",
      description: "Implement the quick sort algorithm to sort an array.",
    },
    {
      title: "Heap Sort",
      description: "Implement the heap sort algorithm to sort an array.",
    },
    {
      title: "Counting Sort",
      description: "Implement the counting sort algorithm for integers with a limited range.",
    },
    {
      title: "Bucket Sort",
      description: "Implement the bucket sort algorithm for floating-point numbers.",
    },
  ],
  "searching": [
    {
      title: "Binary Search",
      description: "Implement binary search to find an element in a sorted array.",
    },
    {
      title: "Search in Rotated Sorted Array",
      description: "Search for a target value in a rotated sorted array.",
    },
    {
      title: "Search a 2D Matrix",
      description: "Search for a value in an m x n matrix with sorted rows and columns.",
    },
    {
      title: "Find Peak Element",
      description: "Find a peak element in an array where adjacent elements are different.",
    },
    {
      title: "Search Range",
      description: "Find the starting and ending position of a target value in a sorted array.",
    },
  ],
  "database": [
    {
      title: "Database Query Executor",
      description: "Write a function that connects to a SQLite database, executes a given SQL SELECT query, and returns the results as a list of dictionaries (where each dictionary represents a row with column names as keys).",
    },
    {
      title: "Database Connection Manager",
      description: "Implement a database connection manager that handles connection pooling and automatic cleanup for SQLite databases.",
    },
    {
      title: "SQL Query Builder",
      description: "Create a simple SQL query builder that can construct SELECT, INSERT, UPDATE, and DELETE queries programmatically.",
    },
    {
      title: "Database Migration Tool",
      description: "Implement a simple database migration tool that can create tables, add columns, and modify existing database schema.",
    },
    {
      title: "Data Validation and Sanitization",
      description: "Write functions to validate and sanitize user input before inserting into a database to prevent SQL injection attacks.",
    },
  ],
};

const languageJson = [
  { id: 95, name: "Go 1.18.5", languageId: 95 },
  { id: 94, name: "TypeScript 5.0.3", languageId: 94 },
  { id: 93, name: "JavaScript Node.js 18.15.0", languageId: 93 },
  { id: 92, name: "Python 3.11.2", languageId: 92 },
  { id: 91, name: "Java JDK 17.0.6", languageId: 91 },
  { id: 90, name: "Dart 2.19.2", languageId: 90 },
  { id: 89, name: "Multi-file program", languageId: 89 },
  { id: 88, name: "Groovy 3.0.3", languageId: 88 },
  { id: 87, name: "F# .NET Core SDK 3.1.202", languageId: 87 },
  { id: 86, name: "Clojure 1.10.1", languageId: 86 },
  { id: 85, name: "Perl 5.28.1", languageId: 85 },
  { id: 84, name: "Visual Basic.Net vbnc 0.0.0.5943", languageId: 84 },
  { id: 83, name: "Swift 5.2.3", languageId: 83 },
  { id: 82, name: "SQL SQLite 3.27.2", languageId: 82 },
  { id: 81, name: "Scala 2.13.2", languageId: 81 },
  { id: 80, name: "R 4.0.0", languageId: 80 },
  { id: 79, name: "Objective-C Clang 7.0.1", languageId: 79 },
  { id: 78, name: "Kotlin 1.3.70", languageId: 78 },
  { id: 77, name: "COBOL GnuCOBOL 2.2", languageId: 77 },
  { id: 76, name: "C++ Clang 7.0.1", languageId: 76 },
  { id: 75, name: "C Clang 7.0.1", languageId: 75 },
  { id: 74, name: "TypeScript 3.7.4", languageId: 74 },
  { id: 73, name: "Rust 1.40.0", languageId: 73 },
  { id: 72, name: "Ruby 2.7.0", languageId: 72 },
  { id: 71, name: "Python 3.8.1", languageId: 71 },
  { id: 70, name: "Python 2.7.17", languageId: 70 },
  { id: 69, name: "Prolog GNU Prolog 1.4.5", languageId: 69 },
  { id: 68, name: "PHP 7.4.1", languageId: 68 },
  { id: 67, name: "Pascal FPC 3.0.4", languageId: 67 },
  { id: 66, name: "Octave 5.1.0", languageId: 66 },
  { id: 65, name: "OCaml 4.09.0", languageId: 65 },
  { id: 64, name: "Lua 5.3.5", languageId: 64 },
  { id: 63, name: "JavaScript Node.js 12.14.0", languageId: 63 },
  { id: 62, name: "Java OpenJDK 13.0.1", languageId: 62 },
  { id: 61, name: "Haskell GHC 8.8.1", languageId: 61 },
  { id: 60, name: "Go 1.13.5", languageId: 60 },
  { id: 59, name: "Fortran GFortran 9.2.0", languageId: 59 },
  { id: 58, name: "Erlang OTP 22.2", languageId: 58 },
  { id: 57, name: "Elixir 1.9.4", languageId: 57 },
  { id: 56, name: "D DMD 2.089.1", languageId: 56 },
  { id: 55, name: "Common Lisp SBCL 2.0.0", languageId: 55 },
  { id: 54, name: "C++ GCC 9.2.0", languageId: 54 },
  { id: 53, name: "C++ GCC 8.3.0", languageId: 53 },
  { id: 52, name: "C++ GCC 7.4.0", languageId: 52 },
  { id: 51, name: "C# Mono *********", languageId: 51 },
  { id: 50, name: "C GCC 9.2.0", languageId: 50 },
  { id: 49, name: "C GCC 8.3.0", languageId: 49 },
  { id: 48, name: "C GCC 7.4.0", languageId: 48 },
  { id: 47, name: "Basic FBC 1.07.1", languageId: 47 },
  { id: 46, name: "Bash 5.0.0", languageId: 46 },
  { id: 45, name: "Assembly NASM 2.14.02", languageId: 45 },
  { id: 44, name: "Executable", languageId: 44 },
  { id: 43, name: "Plain Text", languageId: 43 },
];

const industryJson = [
  { id: 1, name: "Consulting & Professional Services", industryId: 1 },
  { id: 2, name: "Information Technology", industryId: 2 },
  { id: 3, name: "Recruitment", industryId: 3 },
  { id: 4, name: "Building & Construction", industryId: 4 },
  { id: 5, name: "Digital Media", industryId: 5 },
  { id: 6, name: "Energy, Mining & Resources", industryId: 6 },
  { id: 7, name: "Engineering", industryId: 7 },
  { id: 8, name: "Insurance", industryId: 8 },
  { id: 9, name: "Media", industryId: 9 },
  { id: 10, name: "Banking & Finance", industryId: 10 },
  { id: 11, name: "Telecommunications", industryId: 11 },
  { id: 12, name: "Government", industryId: 12 },
  { id: 13, name: "Hospitality & Tourism", industryId: 13 },
  { id: 14, name: "Medical & Healthcare", industryId: 14 },
  { id: 15, name: "Education & Training", industryId: 15 },
  { id: 16, name: "Automobile", industryId: 16 },
  { id: 17, name: "Chemical", industryId: 17 },
  { id: 18, name: "Public services", industryId: 18 },
  { id: 19, name: "Retail", industryId: 19 },
  { id: 20, name: "Transport", industryId: 20 }
];

const departmentJson = [
  { id: 1, name: "Administrative", departmentId: 1 },
  { id: 2, name: "Strategy", departmentId: 2 },
  { id: 3, name: "Finance", departmentId: 3 },
  { id: 4, name: "Sales & Marketing", departmentId: 4 },
  { id: 5, name: "R&D", departmentId: 5 },
  { id: 6, name: "Information Technology", departmentId: 6 },
  { id: 7, name: "Customer Service", departmentId: 7 },
  { id: 8, name: "Human Resources", departmentId: 8 },
  { id: 9, name: "Legal", departmentId: 9 },
  { id: 10, name: "Design", departmentId: 10 },
  { id: 11, name: "Communications", departmentId: 11 },
  { id: 12, name: "Governance", departmentId: 12 },
  { id: 13, name: "Production", departmentId: 13 },
  { id: 14, name: "Sourcing", departmentId: 14 },
  { id: 15, name: "Quality Management", departmentId: 15 },
  { id: 16, name: "Distribution", departmentId: 16 },
  { id: 17, name: "Operations", departmentId: 17 },
  { id: 18, name: "Training/Safety", departmentId: 18 },
  { id: 19, name: "Other", departmentId: 19 }
];

@Injectable()
export class AssessmentChatbotService {
  private readonly logger = new Logger(AssessmentChatbotService.name);
  private readonly llm: ChatOpenAI;
  private readonly sessions: Map<string, ChatSession> = new Map();
  private readonly conversationHistories: Map<string, BaseMessage[]> = new Map();

  constructor(
    private readonly configService: ConfigService,
    private readonly domainQuestionsService: DomainQuestionsService,
    private readonly takeHomeTaskService: TakeHomeTaskService,
    private readonly liveCodingService: LiveCodingService,
  ) {
    const openaiApiKey = this.configService.get<string>('OPEN_AI_SECRET_KEY');
    if (!openaiApiKey) {
      throw new Error('OPEN_AI_SECRET_KEY environment variable is not set');
    }

    this.llm = new ChatOpenAI({
      openAIApiKey: openaiApiKey,
      modelName: 'gpt-4o-mini',
      temperature: 0.7,
    });
  }

  async handleConversation(dto: ConversationDto): Promise<ChatResponse> {
    if (!dto.sessionId || typeof dto.sessionId !== 'string' || !dto.sessionId.trim()) {
      throw new HttpException('Valid UUID sessionId is required', HttpStatus.BAD_REQUEST);
    }


    if (!dto.userInput || typeof dto.userInput !== 'string') {
      throw new HttpException('Valid userInput is required', HttpStatus.BAD_REQUEST);
    }

    let sessionId = dto.sessionId;
    let session = this.sessions.get(sessionId);

    console.log('[startConversation] Session lookup:', {
      sessionId,
      sessionFound: !!session,
      currentStep: session?.currentStep,
      completed: session?.completed,
      userInput: dto.userInput,
      isFromWorkflow: session?.isFromWorkflow,
      workflowContext: session?.workflowContext
    });


    const isChatReset = this.detectChatReset(dto.userInput);

    if (isChatReset) {

      this.sessions.delete(sessionId);
      this.conversationHistories.delete(sessionId);

      return {
        response: 'Chat has been reset. How can I help you today?\n\nI can help you create different types of assessments:\n\n• **Take-Home Assessment** - Coding assignments with questions, test cases, and JDBC packages (say "take-home")\n• **Live Coding Assessment** - Real-time coding challenges (say "live-coding")\n• **Domain Assessment** - Knowledge-based questions for specific domains (say "domain")\n\nWhich type of assessment would you like to create?',
        sessionId,
        currentStep: 'start',
        assessmentType: undefined,
        completed: false,
        data: {
          companyId: dto.companyId,
          userId: dto.userId,
        },
      };
    }


    if (!session) {
      console.log('[startConversation] Creating new session:', {
        sessionId,
        workflowContext: dto.workflowContext,
        userInput: dto.userInput
      });

      // Always offer workflow assignment after assessment creation
      const isFromWorkflow = true;

      session = {
        sessionId,
        currentStep: 'start',
        data: {
          companyId: dto.companyId,
          userId: dto.userId,
        },
        completed: false,
        createdAt: new Date(),
        isFromWorkflow,
        workflowContext: dto.workflowContext,
      };
      this.sessions.set(sessionId, session);
      this.conversationHistories.set(sessionId, []);

    } else {
      console.log('[startConversation] Using existing session:', {
        sessionId,
        currentStep: session.currentStep,
        completed: session.completed,
        isFromWorkflow: session.isFromWorkflow
      });
    }

    const conversationHistory = this.conversationHistories.get(sessionId) || [];


    conversationHistory.push(new HumanMessage(dto.userInput));


    const responseText = await this.processCurrentStep(dto.userInput, session);


    conversationHistory.push(new AIMessage(responseText));
    this.conversationHistories.set(sessionId, conversationHistory);


    if (session.currentStep === 'submit_assessment' && dto.userInput.toLowerCase().trim() === 'yes') {
      const assessmentData = await this.createAssessmentData(session);
      session.data.jsonOutput = assessmentData;
      const savedAssessment = await this.saveAssessment(session);

      // Update jsonOutput with the assessment ID
      if (savedAssessment?.response?.id) {
        session.data.assessmentId = savedAssessment.response.id;
        session.data.jsonOutput.id = savedAssessment.response.id; // Add ID to jsonOutput
      } else if (savedAssessment?.id) {
        session.data.assessmentId = savedAssessment.id;
        session.data.jsonOutput.id = savedAssessment.id; // Add ID to jsonOutput
      }

      const workflowContextCheck = this.checkSimpleWorkflowContext(sessionId);
      console.log('[AssessmentChatbotService] Workflow context check:', {
        workflowContext: session.workflowContext?.isFromWorkflow,
        isFromWorkflow: session.isFromWorkflow,
        simpleWorkflowCheck: workflowContextCheck,
      });

      if (session.workflowContext?.isFromWorkflow || session.isFromWorkflow || workflowContextCheck) {
        session.currentStep = 'workflow_assignment';
        session.completed = false; // Ensure session is not marked as completed yet

        // Save the updated session state
        this.sessions.set(sessionId, session);

        console.log('[AssessmentChatbotService] Setting workflow assignment step:', {
          sessionId,
          currentStep: session.currentStep,
          assessmentId: session.data.assessmentId,
          assessmentName: session.data.name,
        });

        return {
          response: `Assessment submitted successfully!\n\n🔗 Would you like to assign this assessment to your workflow? (yes/no)`,
          sessionId,
          currentStep: session.currentStep,
          assessmentType: session.assessmentType,
          completed: false,
          data: session.data,
          assessmentCreated: true,
          assessmentId: session.data.assessmentId,
          assessmentName: session.data.name,
          workflowAssignment: {
            shouldReturnToWorkflow: true,
            workflowTitle: 'Your Workflow',
          },
        };
      }

      session.completed = true;

      console.log('[AssessmentChatbotService] Regular assessment created with ID:', session.data.assessmentId, 'for type:', session.assessmentType);

      return {
        response: `Assessment submitted successfully!`,
        sessionId,
        currentStep: session.currentStep,
        assessmentType: session.assessmentType,
        completed: session.completed,
        data: session.data,
        assessmentCreated: true,
        assessmentId: session.data.assessmentId,
        assessmentName: session.data.name,
      };
    }

    // Check if this was a workflow assignment completion
    if (session.workflowAssignmentResult) {
      const result = session.workflowAssignmentResult;
      // Clear the stored result
      delete session.workflowAssignmentResult;
      return result;
    }

    return {
      response: responseText,
      sessionId,
      currentStep: session.currentStep,
      assessmentType: session.assessmentType,
      completed: session.completed,
      data: session.data,
    };
  }



  private async processCurrentStep(message: string, session: ChatSession): Promise<string> {
    console.log('[processCurrentStep] Processing step:', {
      sessionId: session.sessionId,
      currentStep: session.currentStep,
      completed: session.completed,
      message: message,
      isFromWorkflow: session.isFromWorkflow,
      workflowContext: session.workflowContext
    });

    if (session.completed && this.isNewAssessmentRequest(message)) {
      return this.resetSessionForNewAssessment(message, session);
    }

    // Handle workflow assignment before checking completed status
    if (session.currentStep === 'workflow_assignment') {
      console.log('[processCurrentStep] Handling workflow assignment with message:', message);
      const workflowAssignmentResult = await this.handleWorkflowAssignment(message, session);

      // Store the workflow assignment result in the session for the main handler to pick up
      session.workflowAssignmentResult = workflowAssignmentResult;

      return workflowAssignmentResult.response;
    }

    // Handle workflow return - don't call handleCompletedSession for workflow returns
    if (session.completed && session.currentStep === 'workflow_return') {
      // This should not happen as LangGraph should handle the workflow return
      // But if it does, just return a simple message
      return `Assessment '${session.data.name}' has been processed. Returning to workflow creation.`;
    }

    if (session.completed) {
      return this.handleCompletedSession(message, session);
    }

    if (session.currentStep === 'start') {
      return this.handleStart(message, session);
    } else if (session.assessmentType === AssessmentType.TAKE_HOME) {
      return this.handleTakeHome(message, session);
    } else if (session.assessmentType === AssessmentType.LIVE_CODING) {
      return this.handleLiveCoding(message, session);
    } else if (session.assessmentType === AssessmentType.DOMAIN) {
      return this.handleDomainAssessment(message, session);
    }

    return "I'm not sure how to help with that. Please start over.";
  }

  private handleStart(message: string, session: ChatSession): string {
    const messageLower = message.toLowerCase();

    if (messageLower.includes('take-home') || messageLower.includes('takehome') || messageLower.includes('take home')) {
      session.assessmentType = AssessmentType.TAKE_HOME;
      session.currentStep = 'collect_name';
      return 'Great! I\'ll help you create a Take-Home Assessment. Let\'s start by collecting the assessment name. What would you like to name this assessment?';
    } else if (messageLower.includes('live') || messageLower.includes('live-coding') || messageLower.includes('live coding')) {
      session.assessmentType = AssessmentType.LIVE_CODING;
      session.currentStep = 'collect_name';
      return 'Excellent! I\'ll help you create a Live Coding Assessment. Let\'s start by collecting the assessment name. What would you like to name this assessment?';
    } else if (messageLower.includes('domain') || messageLower.includes('domain assessment')) {
      session.assessmentType = AssessmentType.DOMAIN;
      session.currentStep = 'collect_name';
      return 'Perfect! I\'ll help you create a Domain Assessment. Let\'s start by collecting the assessment name. What would you like to name this assessment?';
    }

    return `Hello! I'm your Assessment Creation Assistant. I can help you create different types of assessments:

• **Take-Home Assessment** - Coding assignments with questions, test cases, and JDBC packages (say "take-home")
• **Live Coding Assessment** - Real-time coding challenges (say "live-coding")
• **Domain Assessment** - Knowledge-based questions for specific domains (say "domain")

Which type of assessment would you like to create?`;
  }



  private async handleTakeHome(message: string, session: ChatSession): Promise<string> {
    const step = session.currentStep;

    if (step === 'collect_name') {
      if (message.trim().length < 3) {
        return 'Assessment name must be at least 3 characters long. Please provide a valid name.';
      }
      session.data.name = message.trim();
      session.currentStep = 'collect_num_questions';
      return `Assessment name set to: '${message.trim()}'. Now, how many questions would you like in this assessment (1-10)?`;
    } else if (step === 'collect_num_questions') {
      const numQuestions = parseInt(message.trim(), 10);
      if (isNaN(numQuestions) || numQuestions < 1 || numQuestions > 10) {
        return 'Please enter a valid number of questions between 1 and 10.';
      }
      session.data.numQuestions = numQuestions;
      session.currentStep = 'collect_language';
      return `Great! ${numQuestions} questions will be included. What programming language should this assessment focus on?`;
    } else if (step === 'collect_language') {
      const language = languageJson.find(l => l.name.toLowerCase().includes(message.toLowerCase()));
      if (!language) {
        return `Please select a valid programming language from: ${languageJson.map(l => l.name).join(', ')}`;
      }
      session.data.language = language.name;
      session.data.languageId = language.languageId;
      session.currentStep = 'collect_test_cases';
      return `Programming language set to: ${language.name}. Should test cases be included? (Yes/No)`;
    } else if (step === 'collect_test_cases') {
      const responseLower = message.toLowerCase().trim();
      if (!['yes', 'y', 'no', 'n'].includes(responseLower)) {
        return 'Please respond with "yes" or "no" to indicate if test cases should be included.';
      }
      session.data.includeTestCases = responseLower === 'yes' || responseLower === 'y';
      session.currentStep = 'collect_jdbc_package';
      return 'Test cases requirement noted. Should the SQLite JDBC package be included? (Yes/No)';
    } else if (step === 'collect_jdbc_package') {
      const responseLower = message.toLowerCase().trim();
      if (!['yes', 'y', 'no', 'n'].includes(responseLower)) {
        return 'Please respond with "yes" or "no" to indicate if the SQLite JDBC package should be included.';
      }
      session.data.includeJdbc = responseLower === 'yes' || responseLower === 'y';
      session.data.jdbcPackage = session.data.includeJdbc ? 'sqlite-jdbc' : null;
      session.data.packageId = session.data.includeJdbc ? 3 : null;
      session.currentStep = 'collect_topic';
      return this.getTopicSelectionMessage(session);
    } else if (step === 'collect_topic') {
      return this.handleTopicSelection(message, session);
    } else if (step === 'generate_questions') {
      return this.generateQuestions(session);
    } else if (step === 'confirm_questions') {
      const responseLower = message.toLowerCase().trim();
      if (['yes', 'y', 'confirm', 'ok', 'okay'].includes(responseLower)) {
        session.currentStep = 'submit_assessment';
        return `Perfect! Your take-home assessment '${session.data.name}' is ready to be submitted with ${session.data.numQuestions} questions. Would you like to submit this assessment? (yes/no)`;
      } else if (['no', 'n', 'regenerate', 'redo'].includes(responseLower)) {
        session.currentStep = 'generate_questions';
        return this.generateQuestions(session);
      }
      return "Please respond with 'yes' to confirm the questions or 'no' to regenerate them.";
    } else if (step === 'submit_assessment') {
      const responseLower = message.toLowerCase().trim();
      if (['no', 'n', 'cancel'].includes(responseLower)) {
        return 'Assessment submission cancelled. You can modify any details or submit when ready.';
      }
      return 'Please respond with "yes" to submit the assessment or "no" to cancel.';
    }

    return 'I\'m not sure how to process that. Please try again.';
  }

  private async handleLiveCoding(message: string, session: ChatSession): Promise<string> {
    const step = session.currentStep;

    if (step === 'collect_name') {
      if (message.trim().length < 3) {
        return 'Assessment name must be at least 3 characters long. Please provide a valid name.';
      }
      session.data.name = message.trim();
      session.currentStep = 'collect_language';
      return `Assessment name set to: '${message.trim()}'. What programming language should this live coding assessment focus on?`;
    } else if (step === 'collect_language') {
      const language = languageJson.find(l => l.name.toLowerCase().includes(message.toLowerCase()));
      if (!language) {
        return `Please select a valid programming language from: ${languageJson.map(l => l.name).join(', ')}`;
      }
      session.data.language = language.name;
      session.data.languageId = language.languageId;
      session.currentStep = 'collect_description';
      return `Programming language set to: ${language.name}. Please provide a description for this live coding assessment:`;
    } else if (step === 'collect_description') {
      if (message.trim().length < 10) {
        return 'Description must be at least 10 characters long. Please provide a more detailed description.';
      }
      session.data.description = message.trim();
      session.currentStep = 'submit_assessment';
      return `Perfect! Your live coding assessment '${session.data.name}' is ready to be submitted. Would you like to submit this assessment? (yes/no)`;
    } else if (step === 'submit_assessment') {
      const responseLower = message.toLowerCase().trim();
      if (['no', 'n', 'cancel'].includes(responseLower)) {
        return 'Assessment submission cancelled. You can modify any details or submit when ready.';
      }
      return 'Please respond with "yes" to submit the assessment or "no" to cancel.';
    }

    return 'I\'m not sure how to process that. Please try again.';
  }

  private async handleDomainAssessment(message: string, session: ChatSession): Promise<string> {
    const step = session.currentStep;

    // Handle edit/change requests
    if (this.isEditRequest(message)) {
      return this.handleDomainEditRequest(message, session);
    }

    if (step === 'collect_name') {
      if (message.trim().length < 3) {
        return 'Assessment name must be at least 3 characters long. Please provide a valid name.';
      }
      session.data.name = message.trim();
      session.currentStep = 'collect_industry';
      return `Assessment name set to: '${message.trim()}'. What Industry is this assessment for?\n\nAvailable industries:\n${industryJson.map((i, index) => `${index + 1}. ${i.name}`).join('\n')}`;
    } else if (step === 'collect_industry') {
      let industry: typeof industryJson[0] | undefined;

      // Check if input is a number (1-20)
      const inputNumber = parseInt(message.trim(), 10);
      if (!isNaN(inputNumber) && inputNumber >= 1 && inputNumber <= industryJson.length) {
        industry = industryJson[inputNumber - 1]; // Array is 0-indexed, but display is 1-indexed
      } else {
        // Try to match by name
        industry = industryJson.find(i => i.name.toLowerCase().includes(message.toLowerCase()));
      }

      if (!industry) {
        return `Please select a valid industry from:\n\n${industryJson.map((i, index) => `${index + 1}. ${i.name}`).join('\n')}`;
      }

      session.data.industry = industry.name;
      session.data.industryId = industry.industryId;
      session.currentStep = 'collect_department';
      return `Industry set to: ${industry.name}. What Department is this assessment for?\n\nAvailable departments:\n${departmentJson.map((d, index) => `${index + 1}. ${d.name}`).join('\n')}`;
    } else if (step === 'collect_department') {
      let department: typeof departmentJson[0] | undefined;

      // Check if input is a number (1-19)
      const inputNumber = parseInt(message.trim(), 10);
      if (!isNaN(inputNumber) && inputNumber >= 1 && inputNumber <= departmentJson.length) {
        department = departmentJson[inputNumber - 1]; // Array is 0-indexed, but display is 1-indexed
      } else {
        // Try to match by name
        department = departmentJson.find(d => d.name.toLowerCase().includes(message.toLowerCase()));
      }

      if (!department) {
        return `Please select a valid department from:\n\n${departmentJson.map((d, index) => `${index + 1}. ${d.name}`).join('\n')}`;
      }

      // Handle "Other" department selection
      if (department.name === "Other") {
        session.data.department = department.name;
        session.data.departmentId = department.departmentId;
        session.currentStep = 'collect_custom_department';
        return `You've selected "Other" department. Please enter the specific department name:`;
      }

      session.data.department = department.name;
      session.data.departmentId = department.departmentId;
      session.currentStep = 'collect_question_source';
      return `Department set to: ${department.name}. How would you like to create questions for this assessment?\n\nPlease choose:\n• **"existing"** - Use existing domain questions from our database\n• **"manual"** or **"new"** - Create new questions manually (step-by-step)\n• **"ai"** or **"generate"** - Generate questions automatically using AI\n\nWhat's your preference?`;
    } else if (step === 'collect_custom_department') {
      if (message.trim().length < 2) {
        return 'Department name must be at least 2 characters long. Please provide a valid department name:';
      }
      session.data.additionalDepartment = message.trim();
      session.currentStep = 'collect_question_source';
      return `Custom department set to: ${message.trim()}. How would you like to create questions for this assessment?\n\nPlease choose:\n• **"existing"** - Use existing domain questions from our database\n• **"manual"** or **"new"** - Create new questions manually (step-by-step)\n• **"ai"** or **"generate"** - Generate questions automatically using AI\n\nWhat's your preference?`;
    } else if (step === 'collect_question_source') {
      const sourceLower = message.toLowerCase().trim();
      if (sourceLower.includes('existing') || sourceLower === 'existing') {
        session.data.useExistingQuestions = true;
        session.data.useAIGenerated = false;
        session.currentStep = 'collect_duration';
        return `You've chosen to use existing domain questions. What should be the duration for this assessment in minutes (e.g., 30, 60, 90)?`;
      } else if (sourceLower.includes('new') || sourceLower === 'new' || sourceLower.includes('manual')) {
        session.data.useExistingQuestions = false;
        session.data.useAIGenerated = false;
        session.currentStep = 'collect_num_new_questions';
        return `You've chosen to create new questions manually. How many new questions would you like to create (1-20)?`;
      } else if (sourceLower.includes('ai') || sourceLower.includes('generate') || sourceLower.includes('generated')) {
        session.data.useExistingQuestions = false;
        session.data.useAIGenerated = true;
        session.currentStep = 'collect_num_ai_questions';
        return `You've chosen to generate questions using AI. How many questions would you like me to generate (1-20)?`;
      } else {
        return 'Please respond with:\n• "existing" - to use existing domain questions\n• "new" or "manual" - to create new questions manually\n• "ai" or "generate" - to generate questions using AI';
      }
    } else if (step === 'collect_num_new_questions') {
      const numQuestions = parseInt(message.trim(), 10);
      if (isNaN(numQuestions) || numQuestions < 1 || numQuestions > 20) {
        return 'Please enter a valid number of questions between 1 and 20.';
      }
      session.data.numQuestions = numQuestions;
      session.data.newQuestions = [];
      session.data.currentQuestionIndex = 0;
      session.currentStep = 'collect_new_question_details';
      return `Great! You'll create ${numQuestions} new questions. Let's start with question 1.\n\nPlease provide the question text:`;
    } else if (step === 'collect_num_ai_questions') {
      const numQuestions = parseInt(message.trim(), 10);
      if (isNaN(numQuestions) || numQuestions < 1 || numQuestions > 20) {
        return 'Please enter a valid number of questions between 1 and 20.';
      }
      session.data.numQuestions = numQuestions;
      session.currentStep = 'generate_ai_questions';
      return await this.generateAIQuestions(session);
    } else if (step === 'generate_ai_questions') {
      const responseLower = message.toLowerCase().trim();

      // Check for edit requests
      if (this.isAIQuestionEditRequest(message)) {
        return this.handleAIQuestionEdit(message, session);
      }

      if (['yes', 'y', 'confirm', 'ok', 'okay'].includes(responseLower)) {
        session.currentStep = 'collect_duration';
        return `Perfect! Your AI-generated questions have been confirmed. Now, what should be the duration for this assessment in minutes (e.g., 30, 60, 90)?`;
      } else if (['no', 'n', 'regenerate', 'redo'].includes(responseLower)) {
        session.currentStep = 'generate_ai_questions';
        return await this.generateAIQuestions(session);
      }
      return "Please respond with 'yes' to confirm the AI-generated questions, 'no' to regenerate them, or specify changes like 'change score of question 2 to 20 points'.";
    } else if (step === 'collect_new_question_details') {
      return this.handleNewQuestionCollection(message, session);
    } else if (step === 'collect_duration') {
      const duration = parseInt(message.trim(), 10);
      if (isNaN(duration) || duration < 5 || duration > 300) {
        return 'Please enter a valid duration between 5 and 300 minutes.';
      }
      session.data.duration = duration;
      session.currentStep = 'collect_passing_score';
      return `Duration set to ${duration} minutes. What should be the passing score percentage (e.g., 70, 80, 90)?`;
    } else if (step === 'collect_passing_score') {
      const passingScore = parseInt(message.trim(), 10);
      if (isNaN(passingScore) || passingScore < 0 || passingScore > 100) {
        return 'Please enter a valid passing score between 0 and 100.';
      }
      session.data.passingScore = passingScore;
      session.currentStep = 'submit_assessment';

      const questionSummary = session.data.useExistingQuestions
        ? 'using existing domain questions'
        : `with ${session.data.numQuestions} new questions`;

      const departmentDisplay = session.data.department === "Other" && session.data.additionalDepartment
        ? `${session.data.department} (${session.data.additionalDepartment})`
        : session.data.department;

      return `Perfect! Your domain assessment '${session.data.name}' is ready to be submitted:\n\n` +
        `• Industry: ${session.data.industry}\n` +
        `• Department: ${departmentDisplay}\n` +
        `• Questions: ${questionSummary}\n` +
        `• Duration: ${session.data.duration} minutes\n` +
        `• Passing Score: ${passingScore}%\n\n` +
        `Would you like to submit this assessment? (yes/no)`;
    } else if (step === 'submit_assessment') {
      const responseLower = message.toLowerCase().trim();
      if (['no', 'n', 'cancel'].includes(responseLower)) {
        return 'Assessment submission cancelled. You can modify any details or submit when ready.';
      }
      return 'Please respond with "yes" to submit the assessment or "no" to cancel.';
    }

    return 'I\'m not sure how to process that. Please try again.';
  }

  private handleNewQuestionCollection(message: string, session: ChatSession): string {
    const currentQuestionIndex = session.data.currentQuestionIndex || 0;
    const totalQuestions = session.data.numQuestions || 1;

    if (!session.data.newQuestions) {
      session.data.newQuestions = [];
    }

    // Initialize current question if it doesn't exist
    if (!session.data.newQuestions[currentQuestionIndex]) {
      session.data.newQuestions[currentQuestionIndex] = {};
    }

    const currentQuestion = session.data.newQuestions[currentQuestionIndex];

    // Step 1: Collect question text
    if (!currentQuestion.text) {
      if (message.trim().length < 10) {
        return 'Question text must be at least 10 characters long. Please provide a valid question:';
      }
      currentQuestion.text = message.trim();
      return `Question text set. Now, what type of question is this?\n\nPlease choose:\n• "multiple" - Multiple choice (multiple correct answers)\n• "single" - Single choice (one correct answer)\n• "text" - Text/essay answer`;
    }

    // Step 2: Collect question type
    if (!currentQuestion.type) {
      const typeLower = message.toLowerCase().trim();
      if (['multiple', 'multiple choice', 'multiple-choice'].includes(typeLower)) {
        currentQuestion.type = 'multiple';
        return `Question type set to: Multiple Choice. Please provide the answer options separated by commas (e.g., "Option A, Option B, Option C, Option D"):`;
      } else if (['single', 'single choice', 'single-choice'].includes(typeLower)) {
        currentQuestion.type = 'single';
        return `Question type set to: Single Choice. Please provide the answer options separated by commas (e.g., "Option A, Option B, Option C, Option D"):`;
      } else if (['text', 'essay', 'text answer'].includes(typeLower)) {
        currentQuestion.type = 'text';
        return `Question type set to: Text Answer. Please provide the correct answer or answer guidelines:`;
      } else {
        return 'Please choose a valid question type:\n• "multiple" - Multiple choice\n• "single" - Single choice\n• "text" - Text answer';
      }
    }

    // Step 3: Collect options (for multiple/single choice) or correct answer (for text)
    if (!currentQuestion.options && (currentQuestion.type === 'multiple' || currentQuestion.type === 'single')) {
      const options = message.split(',').map(opt => opt.trim()).filter(opt => opt.length > 0);
      if (options.length < 2) {
        return 'Please provide at least 2 options separated by commas:';
      }
      currentQuestion.options = options;
      return `Options set: ${options.join(', ')}\n\nNow, which option(s) are correct? ${currentQuestion.type === 'multiple' ? 'You can specify multiple correct answers separated by commas' : 'Please specify the single correct answer'}:`;
    }

    if (!currentQuestion.correctAnswer && currentQuestion.type === 'text') {
      if (message.trim().length < 3) {
        return 'Please provide a valid correct answer or answer guidelines:';
      }
      currentQuestion.correctAnswer = message.trim();
      return `Correct answer set. What should be the total score/marks for this question? (e.g., 5, 10, 15):`;
    }

    // Step 4: Collect correct answer(s) for multiple/single choice
    if (!currentQuestion.correctAnswer && (currentQuestion.type === 'multiple' || currentQuestion.type === 'single')) {
      const correctAnswers = message.split(',').map(ans => ans.trim()).filter(ans => ans.length > 0);

      // Validate that correct answers exist in options
      const invalidAnswers = correctAnswers.filter(ans => !currentQuestion.options.includes(ans));
      if (invalidAnswers.length > 0) {
        return `Invalid answer(s): ${invalidAnswers.join(', ')}. Please choose from: ${currentQuestion.options.join(', ')}`;
      }

      if (currentQuestion.type === 'single' && correctAnswers.length > 1) {
        return 'Single choice questions can only have one correct answer. Please specify only one:';
      }

      currentQuestion.correctAnswer = currentQuestion.type === 'single' ? correctAnswers[0] : correctAnswers;
      return `Correct answer(s) set: ${Array.isArray(currentQuestion.correctAnswer) ? currentQuestion.correctAnswer.join(', ') : currentQuestion.correctAnswer}\n\nWhat should be the total score/marks for this question? (e.g., 5, 10, 15):`;
    }

    // Step 5: Collect score/marks
    if (!currentQuestion.score) {
      const score = parseInt(message.trim(), 10);
      if (isNaN(score) || score < 1 || score > 100) {
        return 'Please enter a valid score between 1 and 100:';
      }
      currentQuestion.score = score;

      // Move to next question or finish
      session.data.currentQuestionIndex = currentQuestionIndex + 1;

      if (session.data.currentQuestionIndex < totalQuestions) {
        const nextQuestionNum = session.data.currentQuestionIndex + 1;
        return `Question ${currentQuestionIndex + 1} completed! Now let's create question ${nextQuestionNum}.\n\nPlease provide the question text for question ${nextQuestionNum}:`;
      } else {
        // All questions completed
        session.currentStep = 'collect_duration';
        return `Excellent! All ${totalQuestions} questions have been created successfully.\n\nNow, what should be the duration for this assessment in minutes (e.g., 30, 60, 90)?`;
      }
    }

    return 'I\'m not sure how to process that. Please try again.';
  }

  private formatAnswersForDomain(question: any): string {
    if (question.type === 'text') {
      return question.correctAnswer || '';
    } else if (question.type === 'multiple' || question.type === 'single') {
      if (!question.options || !question.correctAnswer) {
        return '';
      }

      const answers = question.options.map((option: string) => {
        const isCorrect = Array.isArray(question.correctAnswer)
          ? question.correctAnswer.includes(option)
          : question.correctAnswer === option;

        return {
          name: option,
          isCorrect,
          score: isCorrect ? question.score || 0 : 0,
        };
      });

      return JSON.stringify(answers);
    }

    return '';
  }

  private isEditRequest(message: string): boolean {
    const editKeywords = [
      'change', 'modify', 'edit', 'update', 'correct', 'fix',
      'go back', 'previous', 'undo', 'different', 'another'
    ];
    const messageLower = message.toLowerCase();
    return editKeywords.some(keyword => messageLower.includes(keyword));
  }

  private handleDomainEditRequest(message: string, session: ChatSession): string {
    const messageLower = message.toLowerCase();

    if (messageLower.includes('industry')) {
      session.currentStep = 'collect_industry';
      return `Sure! Let's change the industry. What Industry is this assessment for?\n\nAvailable industries:\n${industryJson.map((i, index) => `${index + 1}. ${i.name}`).join('\n')}`;
    } else if (messageLower.includes('department')) {
      session.currentStep = 'collect_department';
      // Clear any existing additional department when changing department
      session.data.additionalDepartment = undefined;
      return `Of course! Let's change the department. What Department is this assessment for?\n\nAvailable departments:\n${departmentJson.map((d, index) => `${index + 1}. ${d.name}`).join('\n')}`;
    } else if (messageLower.includes('name')) {
      session.currentStep = 'collect_name';
      return 'No problem! Let\'s change the assessment name. What would you like to name this assessment?';
    } else if (messageLower.includes('question')) {
      if (session.data.useExistingQuestions !== undefined || session.data.useAIGenerated !== undefined) {
        session.currentStep = 'collect_question_source';
        return `Sure! How would you like to create questions for this assessment?\n\nPlease choose:\n• **"existing"** - Use existing domain questions from our database\n• **"manual"** or **"new"** - Create new questions manually (step-by-step)\n• **"ai"** or **"generate"** - Generate questions automatically using AI\n\nWhat's your preference?`;
      }
    }

    // Generic edit request - ask what they want to change
    return 'What would you like to change? You can say:\n' +
      '• "change industry" - to modify the industry\n' +
      '• "change department" - to modify the department\n' +
      '• "change name" - to modify the assessment name\n' +
      '• "change questions" - to modify question source';
  }

  private isAIQuestionEditRequest(message: string): boolean {
    const messageLower = message.toLowerCase();
    const editPatterns = [
      /change.*score.*question\s*(\d+)/,
      /modify.*score.*question\s*(\d+)/,
      /update.*score.*question\s*(\d+)/,
      /set.*score.*question\s*(\d+)/,
      /question\s*(\d+).*score/,
      /score.*question\s*(\d+)/
    ];

    return editPatterns.some(pattern => pattern.test(messageLower));
  }

  private handleAIQuestionEdit(message: string, session: ChatSession): string {
    const messageLower = message.toLowerCase();

    // Extract question number and new score
    const questionMatch = messageLower.match(/question\s*(\d+)/);
    const scoreMatch = messageLower.match(/(\d+)\s*points?/) || messageLower.match(/to\s*(\d+)/) || messageLower.match(/score\s*(\d+)/);

    if (!questionMatch || !scoreMatch) {
      return 'Please specify which question and the new score. For example: "change score of question 2 to 20 points"';
    }

    const questionNumber = parseInt(questionMatch[1], 10);
    const newScore = parseInt(scoreMatch[1], 10);

    if (!session.data.aiGeneratedQuestions || questionNumber < 1 || questionNumber > session.data.aiGeneratedQuestions.length) {
      return `Invalid question number. Please specify a question between 1 and ${session.data.aiGeneratedQuestions?.length || 0}.`;
    }

    if (newScore < 1 || newScore > 100) {
      return 'Score must be between 1 and 100 points.';
    }

    // Update the score
    session.data.aiGeneratedQuestions[questionNumber - 1].score = newScore;
    session.data.newQuestions = session.data.aiGeneratedQuestions; // Keep in sync

    // Regenerate the display with updated scores
    const questionsDisplay = session.data.aiGeneratedQuestions.map((q, index) => {
      let display = `**Question ${index + 1}:** ${q.text}\n`;
      display += `**Type:** ${q.type === 'single' ? 'Single Choice' : q.type === 'multiple' ? 'Multiple Choice' : 'Text Answer'}\n`;

      if (q.options && (q.type === 'single' || q.type === 'multiple')) {
        display += `**Options:**\n${q.options.map((opt, i) => `  ${String.fromCharCode(65 + i)}. ${opt}`).join('\n')}\n`;
        display += `**Correct Answer(s):** ${Array.isArray(q.correctAnswer) ? q.correctAnswer.join(', ') : q.correctAnswer}\n`;
      } else if (q.type === 'text') {
        display += `**Expected Answer:** ${q.correctAnswer}\n`;
      }

      display += `**Score:** ${q.score} points\n`;
      return display;
    }).join('\n---\n\n');

    return `✅ Updated! Question ${questionNumber} score changed to ${newScore} points.\n\nHere are your updated AI-generated questions:\n\n${questionsDisplay}\n\nDo you want to confirm these questions? (yes/no)`;
  }

  private getTopicSelectionMessage(session: ChatSession): string {
    const topics = Object.keys(QUESTION_BANK);
    const topicList = topics.map((topic, index) => `${index + 1}. ${topic}`).join('\n');

    if (session.data.includeJdbc) {
      return `Great! Since you've included the SQLite JDBC package, I recommend selecting "database" for database-related questions, or choose any other topic.\n\nAvailable topics:\n${topicList}\n\nPlease select a topic by typing the name or number:`;
    }

    return `Perfect! Now please select a programming topic for your questions:\n\nAvailable topics:\n${topicList}\n\nPlease select a topic by typing the name or number:`;
  }

  private handleTopicSelection(message: string, session: ChatSession): string {
    const topics = Object.keys(QUESTION_BANK);
    let selectedTopic: string | undefined;

    // Check if input is a number (1-based index)
    const inputNumber = parseInt(message.trim(), 10);
    if (!isNaN(inputNumber) && inputNumber >= 1 && inputNumber <= topics.length) {
      selectedTopic = topics[inputNumber - 1];
    } else {
      // Try to match by name (case-insensitive, partial match)
      selectedTopic = topics.find(topic =>
        topic.toLowerCase().includes(message.toLowerCase().trim()) ||
        message.toLowerCase().trim().includes(topic.toLowerCase())
      );
    }

    if (!selectedTopic) {
      const topicList = topics.map((topic, index) => `${index + 1}. ${topic}`).join('\n');
      return `Please select a valid topic from:\n\n${topicList}\n\nYou can type the topic name or number.`;
    }

    session.data.selectedTopic = selectedTopic;
    session.currentStep = 'generate_questions';
    return this.generateQuestions(session);
  }

  private generateQuestions(session: ChatSession): string {
    try {
      const selectedTopic = session.data.selectedTopic || 'array';
      const numQuestions = session.data.numQuestions || 1;
      const language = session.data.language || 'Python';

      // Get questions from the static bank
      const topicQuestions = QUESTION_BANK[selectedTopic] || QUESTION_BANK['array'];
      const selectedQuestions = topicQuestions.slice(0, Math.min(numQuestions, topicQuestions.length));

      // Convert to structured format with language-specific details
      const structuredQuestions: StructuredQuestion[] = selectedQuestions.map((q: any) => {
        const starterCode = this.generateStarterCode(q.title, language, session.data.includeJdbc);
        const testCases = this.generateTestCases(q.title);

        return {
          nameQuestion: q.title,
          questionDescription: q.description,
          candidateInstruction: `Implement the ${q.title.toLowerCase()} function in ${language}. Follow the function signature provided in the starter code.`,
          starterCode: starterCode,
          outputType: this.getOutputType(q.title),
          outputDescription: `Returns the result of the ${q.title.toLowerCase()} operation.`,
          testCaseInputs: this.getTestCaseInputs(q.title),
          testCases: testCases
        };
      });

      // Store the structured questions
      session.data.structuredQuestions = structuredQuestions;
      session.data.questions = this.formatQuestionsForDisplay(structuredQuestions);
      session.currentStep = 'confirm_questions';

      return `I've generated ${structuredQuestions.length} question${structuredQuestions.length > 1 ? 's' : ''} from the "${selectedTopic}" topic for your assessment:\n\n${session.data.questions}\n\nDo you want to confirm these questions? (yes/no)`;
    } catch (error) {
      this.logger.error(`Generate questions error: ${error.message}`);
      return `Sorry, I encountered an error generating questions: ${error.message}. Please try again.`;
    }
  }

  private async generateAIQuestions(session: ChatSession): Promise<string> {
    try {
      const numQuestions = session.data.numQuestions || 1;
      const industry = session.data.industry || 'Information Technology';
      const department = session.data.department || 'Information Technology';
      const assessmentName = session.data.name || 'Domain Assessment';

      // Create a prompt for AI to generate domain-specific questions
      const prompt = `Generate ${numQuestions} domain assessment questions for a ${industry} industry, ${department} department assessment titled "${assessmentName}".

For each question, provide:
1. Question text (clear and professional)
2. Question type (choose from: "single", "multiple", "text")
3. If single/multiple choice: provide 4 answer options
4. Correct answer(s)
5. Score (between 5-15 points)

Format the response as a JSON array with this structure:
[
  {
    "text": "Question text here",
    "type": "single|multiple|text",
    "options": ["Option A", "Option B", "Option C", "Option D"] (only for single/multiple),
    "correctAnswer": "correct answer or array of correct answers",
    "score": 10
  }
]

Make questions relevant to ${industry} and ${department} domain knowledge.`;

      const response = await this.llm.invoke(`You are an expert assessment creator. Generate professional, relevant domain questions in valid JSON format.\n\n${prompt}`);

      let aiQuestions: DomainQuestion[];
      try {
        // Extract JSON from the response
        const jsonMatch = response.content.toString().match(/\[[\s\S]*\]/);
        if (!jsonMatch) {
          throw new Error('No valid JSON found in AI response');
        }
        aiQuestions = JSON.parse(jsonMatch[0]);
      } catch (parseError) {
        this.logger.error(`AI response parsing error: ${parseError.message}`);
        throw new Error('Failed to parse AI-generated questions');
      }

      // Validate and store the AI-generated questions
      if (!Array.isArray(aiQuestions) || aiQuestions.length === 0) {
        throw new Error('AI did not generate valid questions');
      }

      // Ensure we have the right number of questions
      aiQuestions = aiQuestions.slice(0, numQuestions);

      // Store the AI-generated questions
      session.data.aiGeneratedQuestions = aiQuestions;
      session.data.newQuestions = aiQuestions; // Use the same field for consistency

      // Format questions for display
      const questionsDisplay = aiQuestions.map((q, index) => {
        let display = `**Question ${index + 1}:** ${q.text}\n`;
        display += `**Type:** ${q.type === 'single' ? 'Single Choice' : q.type === 'multiple' ? 'Multiple Choice' : 'Text Answer'}\n`;

        if (q.options && (q.type === 'single' || q.type === 'multiple')) {
          display += `**Options:**\n${q.options.map((opt, i) => `  ${String.fromCharCode(65 + i)}. ${opt}`).join('\n')}\n`;
          display += `**Correct Answer(s):** ${Array.isArray(q.correctAnswer) ? q.correctAnswer.join(', ') : q.correctAnswer}\n`;
        } else if (q.type === 'text') {
          display += `**Expected Answer:** ${q.correctAnswer}\n`;
        }

        display += `**Score:** ${q.score} points\n`;
        return display;
      }).join('\n---\n\n');

      return `I've generated ${aiQuestions.length} AI-powered question${aiQuestions.length > 1 ? 's' : ''} for your ${industry} - ${department} domain assessment:\n\n${questionsDisplay}\n\nDo you want to confirm these questions? (yes/no)`;
    } catch (error) {
      this.logger.error(`Generate AI questions error: ${error.message}`);
      return `Sorry, I encountered an error generating AI questions: ${error.message}. Please try again or choose a different question source.`;
    }
  }

  private formatQuestionsForDisplay(questions: StructuredQuestion[]): string {
    return questions.map((q, index) => {
      const testCaseCount = q.testCases?.length || 0;
      return `**Question ${index + 1}: ${q.nameQuestion}**\n` +
        `${q.questionDescription}\n\n` +
        `**Starter Code:**\n\`\`\`\n${q.starterCode}\n\`\`\`\n\n` +
        `**Test Cases:** ${testCaseCount} test case${testCaseCount !== 1 ? 's' : ''} included\n` +
        `**Output Type:** ${q.outputType}\n`;
    }).join('\n---\n\n');
  }

  private generateStarterCode(questionTitle: string, language: string, includeJdbc: boolean = false): string {
    const lang = language.toLowerCase();

    if (includeJdbc && questionTitle.toLowerCase().includes('database')) {
      if (lang.includes('python')) {
        return `import sqlite3

def execute_query(db_path, query):
    """
    Execute a SQL query on a SQLite database and return results.

    Args:
        db_path (str): Path to the SQLite database file
        query (str): SQL SELECT query to execute

    Returns:
        list: List of dictionaries representing query results
    """
    # Your code here
    pass`;
      } else if (lang.includes('java')) {
        return `import java.sql.*;
import java.util.*;

public class DatabaseQueryExecutor {
    public static List<Map<String, Object>> executeQuery(String dbPath, String query) {
        // Your code here
        return new ArrayList<>();
    }
}`;
      }
    }

    // Generate starter code based on question type and language
    if (lang.includes('python')) {
      return this.getPythonStarterCode(questionTitle);
    } else if (lang.includes('javascript')) {
      return this.getJavaScriptStarterCode(questionTitle);
    } else if (lang.includes('java')) {
      return this.getJavaStarterCode(questionTitle);
    }

    return `// Implement ${questionTitle} in ${language}\n// Your code here`;
  }

  private getPythonStarterCode(questionTitle: string): string {
    const title = questionTitle.toLowerCase();

    if (title.includes('two sum')) {
      return `def two_sum(nums, target):
    """
    Find two numbers in the array that add up to target.

    Args:
        nums (List[int]): Array of integers
        target (int): Target sum

    Returns:
        List[int]: Indices of the two numbers
    """
    # Your code here
    pass`;
    } else if (title.includes('reverse linked list')) {
      return `class ListNode:
    def __init__(self, val=0, next=None):
        self.val = val
        self.next = next

def reverse_list(head):
    """
    Reverse a singly linked list.

    Args:
        head (ListNode): Head of the linked list

    Returns:
        ListNode: Head of the reversed linked list
    """
    # Your code here
    pass`;
    } else if (title.includes('binary search')) {
      return `def binary_search(nums, target):
    """
    Search for target in a sorted array using binary search.

    Args:
        nums (List[int]): Sorted array of integers
        target (int): Target value to search for

    Returns:
        int: Index of target if found, -1 otherwise
    """
    # Your code here
    pass`;
    }

    return `def solve_problem():
    """
    ${questionTitle} implementation.

    Returns:
        Solution to the problem
    """
    # Your code here
    pass`;
  }

  private getJavaScriptStarterCode(questionTitle: string): string {
    const title = questionTitle.toLowerCase();

    if (title.includes('two sum')) {
      return `/**
 * Find two numbers in the array that add up to target.
 * @param {number[]} nums - Array of integers
 * @param {number} target - Target sum
 * @return {number[]} - Indices of the two numbers
 */
function twoSum(nums, target) {
    // Your code here
}`;
    } else if (title.includes('reverse linked list')) {
      return `/**
 * Definition for singly-linked list.
 */
function ListNode(val, next) {
    this.val = (val===undefined ? 0 : val);
    this.next = (next===undefined ? null : next);
}

/**
 * Reverse a singly linked list.
 * @param {ListNode} head - Head of the linked list
 * @return {ListNode} - Head of the reversed linked list
 */
function reverseList(head) {
    // Your code here
}`;
    }

    return `/**
 * ${questionTitle} implementation.
 * @return Solution to the problem
 */
function solveProblem() {
    // Your code here
}`;
  }

  private getJavaStarterCode(questionTitle: string): string {
    const title = questionTitle.toLowerCase();

    if (title.includes('two sum')) {
      return `public class Solution {
    /**
     * Find two numbers in the array that add up to target.
     * @param nums Array of integers
     * @param target Target sum
     * @return Indices of the two numbers
     */
    public int[] twoSum(int[] nums, int target) {
        // Your code here
        return new int[0];
    }
}`;
    }

    return `public class Solution {
    /**
     * ${questionTitle} implementation.
     * @return Solution to the problem
     */
    public Object solveProblem() {
        // Your code here
        return null;
    }
}`;
  }

  private generateTestCases(questionTitle: string): Array<{ input: string, output: string }> {
    const title = questionTitle.toLowerCase();

    if (title.includes('two sum')) {
      return [
        {
          input: JSON.stringify([{ "nums": [2, 7, 11, 15] }, { "target": 9 }]),
          output: '[0,1]'
        },
        {
          input: JSON.stringify([{ "nums": [3, 2, 4] }, { "target": 6 }]),
          output: '[1,2]'
        },
        {
          input: JSON.stringify([{ "nums": [3, 3] }, { "target": 6 }]),
          output: '[0,1]'
        }
      ];
    } else if (title.includes('database query executor')) {
      return [
        {
          input: JSON.stringify([{ "db_path": "test1.db" }, { "query": "SELECT name FROM users WHERE age > 25" }]),
          output: '[{"name": "Alice"}, {"name": "Bob"}]'
        },
        {
          input: JSON.stringify([{ "db_path": "test1.db" }, { "query": "SELECT id, age FROM users WHERE name = 'Charlie'" }]),
          output: '[{"id": 3, "age": 22}]'
        },
        {
          input: JSON.stringify([{ "db_path": "test2.db" }, { "query": "SELECT * FROM products" }]),
          output: '[{"id": 1, "name": "Laptop", "price": 999}, {"id": 2, "name": "Mouse", "price": 25}]'
        }
      ];
    } else if (title.includes('binary search')) {
      return [
        {
          input: JSON.stringify([{ "nums": [-1, 0, 3, 5, 9, 12] }, { "target": 9 }]),
          output: '4'
        },
        {
          input: JSON.stringify([{ "nums": [-1, 0, 3, 5, 9, 12] }, { "target": 2 }]),
          output: '-1'
        },
        {
          input: JSON.stringify([{ "nums": [1] }, { "target": 1 }]),
          output: '0'
        }
      ];
    } else if (title.includes('reverse linked list')) {
      return [
        {
          input: JSON.stringify([{ "head": [1, 2, 3, 4, 5] }]),
          output: '[5,4,3,2,1]'
        },
        {
          input: JSON.stringify([{ "head": [1, 2] }]),
          output: '[2,1]'
        },
        {
          input: JSON.stringify([{ "head": [] }]),
          output: '[]'
        }
      ];
    } else if (title.includes('valid anagram')) {
      return [
        {
          input: JSON.stringify([{ "s": "anagram" }, { "t": "nagaram" }]),
          output: 'true'
        },
        {
          input: JSON.stringify([{ "s": "rat" }, { "t": "car" }]),
          output: 'false'
        },
        {
          input: JSON.stringify([{ "s": "listen" }, { "t": "silent" }]),
          output: 'true'
        }
      ];
    } else if (title.includes('fibonacci')) {
      return [
        {
          input: JSON.stringify([{ "n": 5 }]),
          output: '5'
        },
        {
          input: JSON.stringify([{ "n": 10 }]),
          output: '55'
        },
        {
          input: JSON.stringify([{ "n": 0 }]),
          output: '0'
        }
      ];
    } else if (title.includes('merge sort')) {
      return [
        {
          input: JSON.stringify([{ "arr": [64, 34, 25, 12, 22, 11, 90] }]),
          output: '[11,12,22,25,34,64,90]'
        },
        {
          input: JSON.stringify([{ "arr": [5, 2, 4, 6, 1, 3] }]),
          output: '[1,2,3,4,5,6]'
        },
        {
          input: JSON.stringify([{ "arr": [1] }]),
          output: '[1]'
        }
      ];
    }

    // Default test cases
    return [
      {
        input: JSON.stringify([{ "input": "test_input" }]),
        output: 'expected_output'
      },
      {
        input: JSON.stringify([{ "input": "edge_case" }]),
        output: 'edge_result'
      }
    ];
  }

  private getOutputType(questionTitle: string): string {
    const title = questionTitle.toLowerCase();

    if (title.includes('two sum') || title.includes('merge') || title.includes('array')) {
      return 'array';
    } else if (title.includes('database') || title.includes('anagram')) {
      return 'list';
    } else if (title.includes('search') || title.includes('count') || title.includes('length')) {
      return 'int';
    } else if (title.includes('valid') || title.includes('detect')) {
      return 'boolean';
    } else if (title.includes('string') || title.includes('palindrome')) {
      return 'string';
    }

    return 'object';
  }

  private getTestCaseInputs(questionTitle: string): Array<{ name: string, type: string, description: string }> {
    const title = questionTitle.toLowerCase();

    if (title.includes('two sum')) {
      return [
        { name: 'nums', type: 'int[]', description: 'Array of integers' },
        { name: 'target', type: 'int', description: 'Target sum value' }
      ];
    } else if (title.includes('database query executor')) {
      return [
        { name: 'db_path', type: 'string', description: 'Path to the SQLite database file' },
        { name: 'query', type: 'string', description: 'SQL SELECT query to execute' }
      ];
    } else if (title.includes('binary search')) {
      return [
        { name: 'nums', type: 'int[]', description: 'Sorted array of integers' },
        { name: 'target', type: 'int', description: 'Target value to search for' }
      ];
    } else if (title.includes('linked list')) {
      return [
        { name: 'head', type: 'ListNode', description: 'Head of the linked list' }
      ];
    } else if (title.includes('anagram')) {
      return [
        { name: 's', type: 'string', description: 'First string' },
        { name: 't', type: 'string', description: 'Second string' }
      ];
    } else if (title.includes('fibonacci')) {
      return [
        { name: 'n', type: 'int', description: 'Position in Fibonacci sequence' }
      ];
    } else if (title.includes('sort')) {
      return [
        { name: 'arr', type: 'int[]', description: 'Array to be sorted' }
      ];
    }

    return [
      { name: 'input', type: 'string', description: 'Input parameter' }
    ];
  }

  private isNewAssessmentRequest(message: string): boolean {
    const messageLower = message.toLowerCase().trim();

    // Only trigger on EXPLICIT new assessment creation phrases
    const explicitCreationPhrases = [
      'i want to create a new assessment',
      'i want to create an assessment',
      'i want to create a domain assessment',
      'i want to create a take-home assessment',
      'i want to create a live coding assessment',
      'create a new assessment',
      'create an assessment',
      'create new assessment',
      'start new assessment',
      'begin new assessment',
      'make a new assessment',
      'make an assessment',
      'new assessment',
      'another assessment',
      'fresh start',
      'restart assessment',
      'start over'
    ];

    // Check for explicit creation phrases only
    return explicitCreationPhrases.some(phrase => messageLower.includes(phrase));
  }

  private resetSessionForNewAssessment(message: string, session: ChatSession): string {
    const previousAssessment = {
      name: session.data.name || 'Unknown',
    };

    // Preserve essential data during reset
    const companyId = session.data.companyId;
    const userId = session.data.userId;

    session.assessmentType = undefined;
    session.currentStep = 'start';
    session.data = {
      companyId: companyId,
      userId: userId,
    };
    session.completed = false;

    const response = this.handleStart(message, session);

    return `Great! I see you've completed your take-home assessment '${previousAssessment.name}'. ${response}`;
  }

  private handleCompletedSession(_message: string, session: ChatSession): string {
    const assessmentName = session.data.name || 'your assessment';

    // Get the correct assessment type display name
    let assessmentTypeDisplay = 'assessment';
    if (session.assessmentType === AssessmentType.TAKE_HOME) {
      assessmentTypeDisplay = 'take-home assessment';
    } else if (session.assessmentType === AssessmentType.LIVE_CODING) {
      assessmentTypeDisplay = 'live coding assessment';
    } else if (session.assessmentType === AssessmentType.DOMAIN) {
      assessmentTypeDisplay = 'domain assessment';
    }

    return `Your ${assessmentTypeDisplay} '${assessmentName}' has been completed successfully! 🎉

Would you like to:
• Create a **new assessment** (just say "new", "create", or "take-home")
• Get **session information** (say "session info" or "details")
• **Start fresh** (say "start fresh" or "begin")

What would you like to do next?`;
  }

  // Fixed method to work with actual ChatSession structure
  private async createAssessmentData(session: ChatSession) {
    try {
      if (session.assessmentType === AssessmentType.TAKE_HOME) {
        const {
          name = 'Untitled Assessment',
          language = 'Python',
          languageId = 92,
          numQuestions = 1,
          includeTestCases = false,
          structuredQuestions = [],
          questions = ''
        } = session.data;

        let finalQuestions = [];

        // Use structured questions if available, otherwise fallback to simple parsing
        if (structuredQuestions && structuredQuestions.length > 0) {
          finalQuestions = structuredQuestions.map((sq, index) => {
            // Ensure we have valid test cases with proper string format
            const validTestCases = sq.testCases && sq.testCases.length > 0
              ? sq.testCases.map(tc => ({
                input: tc.input || JSON.stringify([{ "input": `sample_input_${index + 1}` }]),
                output: tc.output || `sample_output_${index + 1}`,
                checked: null
              }))
              : [
                {
                  input: JSON.stringify([{ "input": `sample_input_${index + 1}` }]),
                  output: `sample_output_${index + 1}`,
                  checked: null
                }
              ];

            return {
              name: sq.nameQuestion || `Question ${index + 1}`,
              description: sq.questionDescription || `Question ${index + 1}`,
              outputDescription: sq.outputDescription || 'Returns the expected output based on the problem requirements.',
              outputType: sq.outputType || 'string',
              candidateInstruction: sq.candidateInstruction || 'Solve the problem using the specified programming language.',
              starterCode: sq.starterCode || null,
              testCaseInputs: sq.testCaseInputs ? JSON.stringify(sq.testCaseInputs) : null,
              languageId: languageId,
              useAIGeneratedTestCases: includeTestCases,
              packageId: session.data.packageId || null,
              databaseId: null,
              testcases: validTestCases,
            };
          });
        } else {
          // Fallback to simple text parsing for backward compatibility
          const questionLines = questions.split('\n').filter(line => line.trim());
          for (let i = 0; i < numQuestions; i++) {
            const questionText = questionLines[i] || `Question ${i + 1}`;
            finalQuestions.push({
              name: `Question ${i + 1}`,
              description: questionText,
              outputDescription: 'Returns the expected output based on the problem requirements.',
              outputType: 'string',
              candidateInstruction: 'Solve the problem using the specified programming language.',
              starterCode: null,
              testCaseInputs: null,
              languageId: languageId,
              useAIGeneratedTestCases: includeTestCases,
              packageId: session.data.packageId || null,
              databaseId: null,
              testcases: [
                {
                  input: JSON.stringify([{ "input": `sample_input_${i + 1}` }]),
                  output: `sample_output_${i + 1}`,
                  checked: null
                }
              ],
            });
          }
        }

        return {
          name,
          description: `Take-home assessment: ${name} with ${numQuestions} question${numQuestions > 1 ? 's' : ''} in ${language}`,
          questions: finalQuestions,
        };
      } else if (session.assessmentType === AssessmentType.LIVE_CODING) {
        const {
          name = 'Untitled Live Coding Assessment',
          languageId = 92,
          description = 'Live coding assessment'
        } = session.data;

        return {
          name,
          description,
          starterCode: '',
          instruction: description,
          languageId,
          status: 'draft',
        };
      } else if (session.assessmentType === AssessmentType.DOMAIN) {
        const {
          name = 'Untitled Domain Assessment',
          industry = 'Consulting & Professional Services',
          department = 'Administrative',
          industryId = 1,
          departmentId = 1,
          duration = 60,
          passingScore = 70,
          useExistingQuestions = false,
          newQuestions = []
        } = session.data;

        // Transform new questions (manual or AI-generated) to the format expected by DomainQuestionsService
        const questionsToTransform = session.data.useAIGenerated ?
          (session.data.aiGeneratedQuestions || []) :
          newQuestions;

        const transformedQuestions = questionsToTransform.map((q, index) => ({
          name: q.text || `Question ${index + 1}`,
          type: q.type || 'text',
          options: q.options || null,
          correctAnswer: Array.isArray(q.correctAnswer) ? q.correctAnswer.join(',') : q.correctAnswer,
          score: q.score || 0,
          answers: this.formatAnswersForDomain(q),
        }));

        let questionCount: string;
        if (useExistingQuestions) {
          questionCount = 'existing questions';
        } else if (session.data.useAIGenerated) {
          questionCount = `${questionsToTransform.length} AI-generated question${questionsToTransform.length > 1 ? 's' : ''}`;
        } else {
          questionCount = `${questionsToTransform.length} manually created question${questionsToTransform.length > 1 ? 's' : ''}`;
        }

        // Handle department display for description
        const departmentDisplay = department === "Other" && session.data.additionalDepartment
          ? `${department} (${session.data.additionalDepartment})`
          : department;

        return {
          name,
          description: `Domain assessment for ${industry} - ${departmentDisplay} with ${questionCount}`,
          industry,
          department,
          duration: duration.toString(),
          passing: passingScore,
          industryId: industryId,
          departmentId: departmentId,
          additionalDepartment: session.data.additionalDepartment || null,
          instruction: `Complete this domain assessment for ${industry} - ${departmentDisplay} within ${duration} minutes. Passing score: ${passingScore}%`,
          questions: transformedQuestions,
          useExistingQuestions,
        };
      }

      throw new Error(`Assessment type ${session.assessmentType} not implemented yet`);
    } catch (error) {
      this.logger.error(`Create assessment data error: ${error.message}`);
      throw new HttpException('Failed to create assessment data', HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  private async saveAssessment(session: ChatSession) {
    try {
      const companyId = session.data.companyId;
      const userId = session.data.userId;
      const jsonOutput = session.data.jsonOutput;

      this.logger.log(`Saving assessment with jsonOutput: ${JSON.stringify(jsonOutput, null, 2)}`);
      this.logger.log(`Company ID: ${companyId}`);

      if (!companyId) {
        throw new Error('Company ID is required for saving assessment');
      }

      let result: any;

      if (session.assessmentType === AssessmentType.TAKE_HOME) {
        // Validate the structure before saving
        this.validateTakeHomeData(jsonOutput);
        this.logger.log(`Validated take-home data structure. Questions count: ${jsonOutput.questions?.length || 0}`);

        // Log the complete data structure being passed to TakeHomeTaskService
        this.logger.log(`Complete jsonOutput structure: ${JSON.stringify(jsonOutput, null, 2)}`);

        // Log each question structure for debugging
        if (jsonOutput.questions) {
          jsonOutput.questions.forEach((q: any, index: number) => {
            this.logger.log(`Question ${index + 1}: ${q.name}, TestCases: ${q.testcases?.length || 0}`);
            if (q.testcases && q.testcases.length > 0) {
              q.testcases.forEach((tc: any, tcIndex: number) => {
                this.logger.log(`  TestCase ${tcIndex + 1}: input="${tc.input}", output="${tc.output}"`);
              });
            }
          });
        }

        result = await this.takeHomeTaskService.create(jsonOutput, companyId);

        // Log the result to see what was actually created
        this.logger.log(`TakeHomeTaskService.create result: ${JSON.stringify(result, null, 2)}`);

        // Verify the created assessment by fetching it back
        if (result?.response?.id) {
          const verifyResult = await this.takeHomeTaskService.findOneById(result.response.id);
          this.logger.log(`Verification fetch - Questions count: ${verifyResult?.questions?.length || 0}`);
          if (verifyResult?.questions) {
            verifyResult.questions.forEach((q: any, index: number) => {
              this.logger.log(`Verified Question ${index + 1}: ${q.name}, TestCases: ${q.testcases?.length || 0}`);
            });
          }
        }
      } else if (session.assessmentType === AssessmentType.LIVE_CODING) {
        if (!userId) {
          throw new Error('User ID is required for live coding assessment');
        }

        const transformedDto: liveCodingDto = {
          name: jsonOutput.name,
          description: jsonOutput.description,
          starterCode: jsonOutput.starterCode || '',
          instruction: jsonOutput.instruction,
          languageId: jsonOutput.languageId,
          status: jsonOutput.status || 'draft',
          packageId: null,
          databaseId: null,
        };

        result = await this.liveCodingService.create(transformedDto, companyId, userId);
      } else if (session.assessmentType === AssessmentType.DOMAIN) {
        if (!userId) {
          throw new Error('User ID is required for domain assessment');
        }

        const transformedDto: CreateDomainAssessmentDto = {
          name: jsonOutput.name,
          duration: jsonOutput.duration,
          passing: jsonOutput.passing,
          companyId: companyId,
          industryId: jsonOutput.industryId || 1,
          departmentId: jsonOutput.departmentId || 1,
          additionalDepartment: jsonOutput.additionalDepartment || null,
          instruction: jsonOutput.instruction,
          questions: jsonOutput.questions || [],
        };

        result = await this.domainQuestionsService.create(transformedDto, userId);
      } else {
        throw new Error(`Assessment type ${session.assessmentType} not implemented yet`);
      }

      this.logger.log(`${session.assessmentType} assessment saved for session ${session.sessionId}`);
      return result;
    } catch (error) {
      this.logger.error(`Save assessment error: ${error.message}`);
      throw new HttpException(`Failed to save assessment: ${error.message}`, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  // Check for workflow context using simple heuristics
  private checkSimpleWorkflowContext(sessionId: string): boolean {
    try {
      const session = this.sessions.get(sessionId);
      if (session) {
        // Only return true if there's explicit workflow context
        // Don't trigger just because someone mentioned assessment types
        const conversationHistory = this.conversationHistories.get(sessionId) || [];
        const hasWorkflowKeywords = conversationHistory.some(msg => {
          if (msg instanceof HumanMessage) {
            const content = String(msg.content).toLowerCase();
            return content.includes('workflow') ||
              content.includes('add to workflow') ||
              content.includes('assign to workflow') ||
              content.includes('workflow creation');
          }
          return false;
        });

        // Only trigger workflow context if there are explicit workflow keywords
        return hasWorkflowKeywords;
      }

      return false;
    } catch (error) {
      console.error('[checkSimpleWorkflowContext] Error:', error);
      return false;
    }
  }

  // Helper function to detect chat reset commands
  private detectChatReset(userInput: string): boolean {
    const resetKeywords = [
      'reset chat', 'clear chat', 'restart chat', 'new chat', 'start over',
      'reset conversation', 'clear conversation', 'restart conversation',
      'exit', 'quit', 'end chat', 'start fresh', 'begin again',
      'reset session', 'clear session', 'new session'
    ];

    const input = userInput.toLowerCase().trim();
    return resetKeywords.some(keyword => input.includes(keyword)) ||
      /^(reset|clear|restart|exit|quit)$/i.test(input);
  }

  // Handle workflow assignment responses
  private async handleWorkflowAssignment(message: string, session: ChatSession): Promise<ChatResponse> {
    const responseLower = message.toLowerCase().trim();

    console.log('[handleWorkflowAssignment] Processing workflow assignment:', {
      sessionId: session.sessionId,
      message,
      responseLower,
      currentStep: session.currentStep,
      sessionData: session.data,
      workflowContext: session.workflowContext,
    });

    if (['yes', 'y', 'assign'].includes(responseLower)) {
      try {
        // Get the assessment ID from session.data.assessmentId (primary source)
        let assessmentId = session.data.assessmentId;
        const assessmentName = session.data.name || 'Untitled Assessment';

        // Fallback to jsonOutput.id if assessmentId is not set
        if (!assessmentId && session.data.jsonOutput?.id) {
          assessmentId = session.data.jsonOutput.id;
        }

        console.log('[handleWorkflowAssignment] Assessment assignment details:', {
          assessmentId,
          assessmentName,
          assessmentType: session.assessmentType,
          sessionData: session.data,
        });

        if (!assessmentId) {
          console.error('[handleWorkflowAssignment] No assessment ID found for assignment');
          // Stay in workflow_assignment step to allow retry
          return {
            response: `I couldn't find the assessment ID for '${assessmentName}'. Please confirm the assessment details or try again. Would you like to assign this assessment to your workflow? (yes/no)`,
            sessionId: session.sessionId,
            currentStep: session.currentStep,
            assessmentType: session.assessmentType,
            completed: false,
            data: session.data,
          };
        }

        // Mark session as completed and set workflow return step
        session.completed = true;
        session.currentStep = 'workflow_return';

        // Store assignment data for the workflow service to pick up
        session.data.workflowAssignment = {
          assessmentId: assessmentId,
          assessmentName: assessmentName,
          assessmentType: session.assessmentType,
          assigned: true,
        };

        // Update session to ensure persistence
        this.sessions.set(session.sessionId, session);

        return {
          response: `Perfect! The assessment '${assessmentName}' (ID: ${assessmentId}) has been assigned to your workflow. Returning to workflow creation. What other steps would you like to include in your workflow?`,
          sessionId: session.sessionId,
          currentStep: session.currentStep,
          assessmentType: session.assessmentType,
          completed: true,
          data: session.data,
          assessmentCreated: true,
          assessmentId: assessmentId,
          assessmentName: assessmentName,
          workflowAssignment: {
            shouldReturnToWorkflow: true,
            workflowTitle: 'Your Workflow',
          },
        };
      } catch (error) {
        console.error('[handleWorkflowAssignment] Error during assignment:', error);
        session.completed = true;
        session.currentStep = 'workflow_return';

        // Check if it's a database connection error
        if (error.name === 'SequelizeHostNotFoundError' || error.message?.includes('ENOTFOUND')) {
          return {
            response: `Assessment '${session.data.name}' has been created successfully! However, there's a database connectivity issue preventing workflow assignment. Please check your database configuration and restart the application. Returning to workflow creation.`,
            sessionId: session.sessionId,
            currentStep: session.currentStep,
            assessmentType: session.assessmentType,
            completed: true,
            data: session.data,
            workflowAssignment: {
              shouldReturnToWorkflow: true,
              workflowTitle: 'Your Workflow',
            },
          };
        }

        return {
          response: `Assessment '${session.data.name}' has been created but there was an error during assignment: ${error.message}. Returning to workflow creation. What other steps would you like to include in your workflow?`,
          sessionId: session.sessionId,
          currentStep: session.currentStep,
          assessmentType: session.assessmentType,
          completed: true,
          data: session.data,
          workflowAssignment: {
            shouldReturnToWorkflow: true,
            workflowTitle: 'Your Workflow',
          },
        };
      }
    } else if (['no', 'n', 'skip'].includes(responseLower)) {
      // Mark session as completed and set workflow return step
      session.completed = true;
      session.currentStep = 'workflow_return';
      return {
        response: `Assessment '${session.data.name}' has been created but not assigned to the workflow. Would you like to create another assessment or continue with workflow creation?`,
        sessionId: session.sessionId,
        currentStep: session.currentStep,
        assessmentType: session.assessmentType,
        completed: true,
        data: session.data,
        workflowAssignment: {
          shouldReturnToWorkflow: true,
          workflowTitle: 'Your Workflow',
        },
      };
    } else {
      return {
        response: 'Please respond with "yes" to assign this assessment to your workflow or "no" to skip assignment.',
        sessionId: session.sessionId,
        currentStep: session.currentStep,
        assessmentType: session.assessmentType,
        completed: false,
        data: session.data,
      };
    }
  }

  // Validation method for take-home assessment data
  private validateTakeHomeData(data: any): void {
    if (!data.name) {
      throw new Error('Assessment name is required');
    }

    if (!data.questions || !Array.isArray(data.questions) || data.questions.length === 0) {
      throw new Error('At least one question is required');
    }

    data.questions.forEach((question: any, index: number) => {
      if (!question.name) {
        throw new Error(`Question ${index + 1}: name is required`);
      }

      if (!question.description) {
        throw new Error(`Question ${index + 1}: description is required`);
      }

      if (!question.outputDescription) {
        throw new Error(`Question ${index + 1}: outputDescription is required`);
      }

      if (!question.outputType) {
        throw new Error(`Question ${index + 1}: outputType is required`);
      }

      if (!question.candidateInstruction) {
        throw new Error(`Question ${index + 1}: candidateInstruction is required`);
      }

      // Validate test cases
      if (!question.testcases || !Array.isArray(question.testcases) || question.testcases.length === 0) {
        throw new Error(`Question ${index + 1}: at least one test case is required`);
      }

      question.testcases.forEach((testcase: any, tcIndex: number) => {
        if (!testcase.input || typeof testcase.input !== 'string') {
          throw new Error(`Question ${index + 1}, TestCase ${tcIndex + 1}: input must be a non-empty string`);
        }

        if (!testcase.output || typeof testcase.output !== 'string') {
          throw new Error(`Question ${index + 1}, TestCase ${tcIndex + 1}: output must be a non-empty string`);
        }
      });
    });

    this.logger.log('Take-home assessment data validation passed');
  }

  // Placeholder for generateQuestion (for compatibility with initial controller)
  async generateQuestion(dto: ConversationDto): Promise<ChatResponse> {
    return this.handleConversation(dto);
  }
}